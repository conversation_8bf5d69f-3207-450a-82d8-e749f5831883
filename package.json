{"name": "server", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start-dev": "nodemon server.js", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"nodemon": "^3.0.3"}, "dependencies": {"@payos/node": "^1.0.10", "agora-access-token": "^2.0.4", "agora-rtc-sdk-ng": "^4.23.4", "axios": "^1.8.4", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-session": "^1.18.1", "google-auth-library": "^9.7.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.12.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1", "uuid": "^11.1.0", "winston": "^3.17.0"}}