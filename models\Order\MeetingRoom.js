const mongoose = require("mongoose");

const OrderMeetingRoomSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    room: {
      type: String,
      required: true,
    },
    startTime: {
      type: Date,
      required: true,
    },
    endTime: {
      type: Date,
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    status: {
      type: String,
      enum: ["pending", "confirmed", "cancelled"],
      default: "pending",
    },
  },
  { timestamps: true, versionKey: false }
);
const OrderMeetingRoom = mongoose.model(
  "OrderMeetingRoom",
  OrderMeetingRoomSchema
);
module.exports = OrderMeetingRoom;
