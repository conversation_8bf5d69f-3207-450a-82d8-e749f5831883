const jwt = require("jsonwebtoken");

const authenticateUser = (req, res, next) => {
  const token = req.cookies?.token;

  if (!token) {
    return res
      .status(401)
      .json({ message: "<PERSON>h<PERSON><PERSON> có token, t<PERSON>y cập bị từ chối" });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
    req.user = decoded;
    next();
  } catch (error) {
    console.error("❌ JWT Error:", error);
    res.status(401).json({ message: "Token không hợp lệ" });
  }
};

module.exports = { authenticateUser };
